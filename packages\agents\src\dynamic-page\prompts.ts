import { PromptConfig } from "@cscs-agent/core";

function createTag() {
  return `<embedded-tag text="企业名称" rawValue="企业名称(id: 123456)" tooltips="企业名称(id: 123456)"></embedded-tag>`;
}

function createEidtableTag() {
  return `<embedded-editable-tag placeholder="请输入字段名称"></embedded-editable-tag>`;
}

const prompts: PromptConfig[] = [
  {
    title: "生成页面",
    description: "根据SQL语句生成页面",
    prompt:
      "生成页面：生成一个页面，查询数据库中的【企业信息表】，在页面列表中展示【企业名称】、【企业性质】、【统一社会信用代码】",
  },
];
